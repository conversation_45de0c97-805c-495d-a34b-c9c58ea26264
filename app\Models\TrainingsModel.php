<?php

namespace App\Models;

use CodeIgniter\Model;

class TrainingsModel extends Model
{
    protected $table            = 'trainings';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    
    protected $allowedFields = [
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'locations',
        'gps',
        'date_start',
        'date_end',
        'topic',
        'objectives',
        'content',
        'trainers',
        'attendees',
        'materials',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    

    // Validation
    protected $validationRules = [
        'country_id'  => 'required|numeric',
        'province_id' => 'required|numeric',
        'district_id' => 'required|numeric',
        'llg_id'      => 'required|numeric',
        'locations'   => 'required',
        'gps'         => 'required',
        'date_start'  => 'required|valid_date',
        'date_end'    => 'required|valid_date',
        'topic'       => 'required|min_length[3]|max_length[255]',
        'objectives'  => 'required',
        'status'      => 'required|numeric'
    ];
    
    protected $validationMessages = [];
    protected $skipValidation     = false;
    protected $cleanValidationRules = true;
}
