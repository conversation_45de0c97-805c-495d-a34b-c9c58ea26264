<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= $title ?? 'AgriStats - Staff Portal' ?></title>

    <!-- CSRF Meta Tag -->
    <?= csrf_meta() ?>

    <!-- icon -->
    <link rel="icon" href="<?= base_url('public/assets/system_img/favicon.ico') ?>">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Toastr CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />


    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>






    <style>
        :root {
            --primary-green: #2e7d32;
            --dark-green: #1b5e20;
            --light-green: #4caf50;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
        }

        /* Navbar */
        .navbar {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
            padding: 1rem;
        }

        .navbar-brand {
            color: white !important;
            font-weight: 600;
        }

        .nav-link {
            color: white !important;
            opacity: 0.9;
        }

        .nav-link:hover {
            opacity: 1;
        }

        /* Sidebar - Desktop behavior (default) */
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            min-height: 100vh;
            background: white;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }

        #sidebar.active {
            margin-left: -250px;
        }

        #sidebar .sidebar-header {
            padding: 20px;
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
            color: white;
        }

        #sidebar ul.components {
            padding: 20px 0;
        }

        #sidebar ul li a {
            padding: 10px 20px;
            display: block;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
        }

        #sidebar ul li a:hover {
            background: #f8f9fa;
            color: var(--primary-green);
        }

        #sidebar ul li a.active {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--dark-green) 100%);
            color: white;
        }

        /* Content - Desktop behavior (default) */
        #content {
            width: 100%;
            min-height: 100vh;
            transition: all 0.3s;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        /* Footer */
        .footer {
            background: white;
            padding: 1rem;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
        }

        /* Responsive - Mobile behavior */
        @media (max-width: 768px) {

            /* Mobile sidebar behavior - hover over content */
            #sidebar {
                margin-left: 0;
                position: fixed;
                top: 0;
                left: 0;
                height: 100%;
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
                z-index: 1040;
                /* Lower z-index to appear below modals (1055) */
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            /* Reverse the meaning of active class for mobile */
            #sidebar.active {
                margin-left: 0;
                /* Override desktop behavior */
                transform: translateX(0);
            }

            #sidebar:not(.active) {
                transform: translateX(-100%);
            }

            /* Sidebar overlay */
            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1039;
                /* Lower z-index to appear below sidebar and modals */
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .sidebar-overlay.active {
                display: block;
                opacity: 1;
            }

            /* Make hamburger button always visible */
            #sidebarCollapse {
                position: fixed;
                top: 15px;
                left: 15px;
                z-index: 1060;
                background-color: var(--primary-green);
                width: 40px;
                height: 40px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
                transition: all 0.3s ease;
            }

            #sidebarCollapse:hover {
                background-color: var(--dark-green);
            }

            /* Close button styling */
            #closeSidebarBtn {
                transition: transform 0.2s ease;
            }

            #closeSidebarBtn:hover {
                transform: scale(1.2);
            }

            /* Adjust navbar to accommodate fixed hamburger button */
            .navbar {
                padding-left: 60px !important;
            }

            /* Add overlay when sidebar is active */
            body::after {
                content: '';
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                /* Below sidebar but above content */
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            body.sidebar-active::after {
                display: block;
                opacity: 1;
            }

            /* Ensure content doesn't shift in mobile */
            #content {
                width: 100%;
                margin-left: 0 !important;
                position: relative;
                z-index: 1030;
                /* Lower than sidebar and overlay */
            }


        }
    </style>
</head>

<body>
    <!-- Add overlay div for mobile sidebar -->
    <div class="sidebar-overlay"></div>

    <div class="d-flex">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header position-relative">
                <h5 class="mb-0">AgriStats</h5>
                <small>Staff Portal</small>
                <!-- Mobile Only Close Button -->
                <button type="button" id="closeSidebarBtn" class="btn btn-close btn-close-white btn-sm position-absolute top-0 end-0 mt-2 me-2 d-md-none" aria-label="Close">
                </button>
            </div>

            <div class="p-3 border-bottom">
                <div class="d-flex align-items-center">
                    <img src="<?= imgcheck('') ?>"
                        class="rounded-circle" width="50" height="50" alt="User">
                    <div class="ms-3">
                        <h6 class="mb-0"><?= session()->get('name') ?? 'Staff Member' ?></h6>
                        <small class="text-muted"><?= session()->get('position') ?? 'Field Officer' ?></small>
                    </div>
                </div>
            </div>

            <ul class="list-unstyled components">

                <!-- Staff Dashboard -->
                <li>
                    <a href="<?= base_url('staff') ?>" class="<?= current_url() == base_url('staff') ? 'active' : '' ?>">
                        <i class="fas fa-tachometer-alt me-2"></i> Staff Dashboard
                    </a>
                </li>
                <li >
                    <a href="<?= base_url('dashboard') ?>" class="<?= str_contains(current_url(), 'dashboard') ? 'active' : '' ?>">
                        <i class="fas fa-user-shield me-2"></i> Admin Dashboard
                    </a>
                </li>

                <!-- Office Information -->
                <li>
                    <a href="#officeSubmenu" data-bs-toggle="collapse"
                        class="<?= (str_contains(current_url(), 'dashboard') ||
                                    str_contains(current_url(), 'exercises') ||
                                    str_contains(current_url(), 'staff/farmers') ||
                                    str_contains(current_url(), 'staff/buyers') ||
                                    str_contains(current_url(), 'staff/office/documents')) ? 'active' : '' ?>">
                        <i class="fas fa-building me-2"></i> Office Information
                        <i class="fas fa-angle-down ms-2"></i>
                    </a>
                    <ul class="collapse list-unstyled <?= (str_contains(current_url(), 'dashboard') ||
                                                            str_contains(current_url(), 'exercises') ||
                                                            str_contains(current_url(), 'staff/farmers') ||
                                                            str_contains(current_url(), 'staff/buyers') ||
                                                            str_contains(current_url(), 'staff/office/documents')) ? 'show' : '' ?>"
                        id="officeSubmenu">

                        <li>
                            <a href="<?= base_url('exercises') ?>" class="<?= str_contains(current_url(), 'exercises') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Exercises
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/farmers') ?>" class="<?= str_contains(current_url(), 'staff/farmers') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Farmers
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/buyers') ?>" class="<?= current_url() == base_url('staff/buyers') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Crop Buyers
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/office/documents') ?>" class="<?= str_contains(current_url(), 'staff/office/documents') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Documents
                            </a>
                        </li>
                        <!-- Temporarily disabled Technical Files menu item
                        <li>
                            <a href="<?= base_url('staff/office/technical-files') ?>" class="<?= str_contains(current_url(), 'staff/office/technical-files') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Technical Files
                            </a>
                        </li>
                        -->
                    </ul>
                </li>


                <!-- Extension Services -->
                <li>
                    <a href="#extensionSubmenu" data-bs-toggle="collapse"
                        class="<?= (str_contains(current_url(), 'staff/extension')) ? 'active' : '' ?>">
                        <i class="fas fa-chalkboard-teacher me-2"></i> Extension Services
                        <i class="fas fa-angle-down ms-2"></i>
                    </a>
                    <ul class="collapse list-unstyled <?= str_contains(current_url(), 'staff/extension') ? 'show' : '' ?>"
                        id="extensionSubmenu">
                        <li>
                            <a href="<?= base_url('staff/extension/field-visits') ?>"
                                class="<?= str_contains(current_url(), 'staff/extension/field-visits') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Field Visits
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/extension/trainings') ?>"
                                class="<?= str_contains(current_url(), 'staff/extension/trainings') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Trainings
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/extension/inputs') ?>"
                                class="<?= str_contains(current_url(), 'staff/extension/inputs') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Inputs
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Crops Information -->
                <li>
                    <a href="#cropsSubmenu" data-bs-toggle="collapse"
                        class="<?= (str_contains(current_url(), 'staff/crops-farm-blocks') ||
                                    str_contains(current_url(), 'crops/data') ||
                                    str_contains(current_url(), 'fertilizer_data')) ? 'active' : '' ?>">
                        <i class="fas fa-seedling me-2"></i> Crops Information
                        <i class="fas fa-angle-down ms-2"></i>
                    </a>
                    <ul class="collapse list-unstyled <?= (str_contains(current_url(), 'staff/crops-farm-blocks') ||
                                                            str_contains(current_url(), 'crops/data') ||
                                                            str_contains(current_url(), 'fertilizer_data')) ? 'show' : '' ?>"
                        id="cropsSubmenu">
                        <li>
                            <a href="<?= base_url('staff/crops-farm-blocks') ?>"
                                class="<?= str_contains(current_url(), 'staff/crops-farm-blocks') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Crops Farm Blocks
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/crops/data') ?>"
                                class="<?= str_contains(current_url(), 'crops/data') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Crops Data
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/farms/fertilizer_data') ?>"
                                class="<?= str_contains(current_url(), 'fertilizer_data') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Fertilizer Data
                            </a>
                        </li>

                    </ul>
                </li>

                <!-- Livestock Information -->
                <li>
                    <a href="<?= base_url('staff/livestock-farm-blocks') ?>"
                        class="<?= (str_contains(current_url(), 'livestock-farm-blocks') ||
                                    str_contains(current_url(), 'livestock/blocks/') ||
                                    str_contains(current_url(), 'livestock/farm-blocks')) ? 'active' : '' ?>">
                        <i class="fas fa-cow me-2"></i> Livestock Farm Blocks
                    </a>
                </li>

                <!-- Reports -->
                <li>
                    <a href="#reportsSubmenu" data-bs-toggle="collapse" class="<?= str_contains(current_url(), 'staff/reports') ? 'active' : '' ?>">
                        <i class="fas fa-chart-bar me-2"></i> Reports
                        <i class="fas fa-angle-down ms-2"></i>
                    </a>
                    <ul class="collapse list-unstyled <?= str_contains(current_url(), 'staff/reports') ? 'show' : '' ?>" id="reportsSubmenu">
                        <li>
                            <a href="<?= base_url('staff/reports/farmers') ?>" class="<?= str_contains(current_url(), 'staff/reports/farmers') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Farmers Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/blocks') ?>" class="<?= str_contains(current_url(), 'staff/reports/blocks') || str_contains(current_url(), 'staff/reports/block_profile') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Farm Blocks Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/crops') ?>" class="<?= str_contains(current_url(), 'reports/crops') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Crops Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/diseases') ?>" class="<?= str_contains(current_url(), 'reports/diseases') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Diseases Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/fertilizer') ?>" class="<?= str_contains(current_url(), 'reports/fertilizer') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Fertilizer Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/pesticides') ?>" class="<?= str_contains(current_url(), 'reports/pesticides') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Pesticides Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/harvests') ?>" class="<?= str_contains(current_url(), 'reports/harvests') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Harvest Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/marketing') ?>" class="<?= str_contains(current_url(), 'reports/marketing') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Marketing Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/livestock-blocks') ?>" class="<?= str_contains(current_url(), 'reports/livestock-blocks') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> LS Farm Block Reports
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/reports/livestock-data') ?>" class="<?= str_contains(current_url(), 'reports/livestock-data') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> LS Farm Data Reports
                            </a>
                        </li>
                    </ul>
                </li>


                <!-- Extra Tools -->
                <li>
                    <a href="#toolsSubmenu" data-bs-toggle="collapse"
                        class="<?= str_contains(current_url(), 'maps') || str_contains(current_url(), 'staff/tools/climate-data') ? 'active' : '' ?>">
                        <i class="fas fa-tools me-2"></i> Extra Tools
                        <i class="fas fa-angle-down ms-2"></i>
                    </a>
                    <ul class="collapse list-unstyled <?= str_contains(current_url(), 'maps') || str_contains(current_url(), 'staff/tools/climate-data') ? 'show' : '' ?>"
                        id="toolsSubmenu">
                        <li>
                            <a href="<?= base_url('staff/farms/maps') ?>" class="<?= str_contains(current_url(), 'maps') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Field Maps
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('staff/tools/climate-data') ?>" class="<?= str_contains(current_url(), 'staff/tools/climate-data') ? 'active' : '' ?>">
                                <i class="fas fa-circle-dot me-2"></i> Climate Data
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-link text-white">
                        <i class="fas fa-bars"></i>
                    </button>
                    <span class=" text-light"> <img src="<?= imgcheck(session('orglogo')) ?>" alt="" style="height: 30px;"> <?= session('orgname') ?></span>

                    <div class="ms-auto d-flex align-items-center">
                        <div class="dropdown me-3">
                            <button class="btn btn-link text-white dropdown-toggle" type="button" id="districtDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-map-marker-alt"></i>
                                <span class="ms-1"><?= session('district_name') ?></span>
                            </button>
                            <ul class="dropdown-menu">
                                <?php if (session('assigned_districts') && is_array(session('assigned_districts'))): ?>
                                    <?php foreach (session('assigned_districts') as $district): ?>
                                        <li>
                                            <a class="dropdown-item <?= $district['id'] == session('district_id') ? 'active' : '' ?>"
                                                href="<?= base_url('staff/switch_district/' . $district['id']) ?>">
                                                <?= $district['name'] ?>
                                                <?php if ($district['is_default']): ?>
                                                    <i class="fas fa-star text-warning ms-2"></i>
                                                <?php endif; ?>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <li><span class="dropdown-item-text">No districts assigned</span></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-link text-white dropdown-toggle" type="button"
                                id="userDropdown" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i>
                                <span class="ms-1"><?= session()->get('username') ?? 'Staff' ?></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-user me-2"></i> Profile
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-cog me-2"></i> Settings
                                    </a>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?= base_url('logout') ?>">
                                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <div class="container-fluid p-4">


                <!-- Page Content -->
                <?= $this->renderSection('content') ?>
            </div>

            <!-- Footer -->
            <footer class="footer mt-auto">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6 text-center text-md-start">
                            <strong>Copyright &copy; <?= date('Y') ?>
                                <a href="#" class="text-success">AgriStats</a>.
                            </strong>
                            All rights reserved.
                        </div>
                        <div class="col-md-6 text-center text-md-end">
                            <small class="text-muted">Powered by Dakoii Systems</small>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>



    <script>
        $(document).ready(function() {
            // Setup CSRF token for all AJAX requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="<?= csrf_header() ?>"]').attr('content')
                },
                beforeSend: function(xhr, settings) {
                    // Only add the token for same-origin POST requests
                    if (!/^(GET|HEAD|OPTIONS)$/i.test(settings.type) && settings.url.indexOf('http') !== 0) {
                        // Add CSRF token as a parameter for form data or URL encoded requests
                        if (settings.data instanceof FormData) {
                            settings.data.append('<?= csrf_token() ?>', '<?= csrf_hash() ?>');
                        } else if (typeof settings.data === 'string') {
                            settings.data = settings.data + '&<?= csrf_token() ?>=' + '<?= csrf_hash() ?>';
                        } else if (settings.data === undefined || settings.data === null) {
                            settings.data = '<?= csrf_token() ?>=' + '<?= csrf_hash() ?>';
                        } else if (typeof settings.data === 'object') {
                            settings.data['<?= csrf_token() ?>'] = '<?= csrf_hash() ?>';
                        }
                    }
                }
            });

            // Function to check if we're in mobile view
            function isMobileView() {
                return window.innerWidth <= 768;
            }

            // Function to open sidebar in mobile
            function openSidebar() {
                $('#sidebar').addClass('active');
                $('.sidebar-overlay').addClass('active');
                console.log('Opening sidebar');
            }

            // Function to close sidebar in mobile
            function closeSidebar() {
                $('#sidebar').removeClass('active');
                $('.sidebar-overlay').removeClass('active');
                console.log('Closing sidebar');
            }

            // Initialize sidebar state
            function initSidebar() {
                if (isMobileView()) {
                    // Start with sidebar closed on mobile
                    closeSidebar();
                } else {
                    // In desktop view, sidebar is visible by default
                    // Remove any mobile-specific classes
                    $('#sidebar').removeClass('active');
                    $('.sidebar-overlay').removeClass('active');
                }
            }

            // Initialize on page load
            initSidebar();

            // Sidebar toggle button
            $('#sidebarCollapse').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('Sidebar toggle clicked, Mobile:', isMobileView());

                if (isMobileView()) {
                    // In mobile: toggle between open/closed
                    if ($('#sidebar').hasClass('active')) {
                        closeSidebar();
                    } else {
                        openSidebar();
                    }
                } else {
                    // In desktop: use the default toggle behavior
                    $('#sidebar').toggleClass('active');
                }
            });

            // Close sidebar button (in the sidebar header)
            $('#closeSidebarBtn').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Close sidebar button clicked');
                closeSidebar();
            });

            // Close sidebar when clicking overlay
            $('.sidebar-overlay').on('click', function() {
                console.log('Overlay clicked');
                closeSidebar();
            });

            // Prevent clicks inside sidebar from closing it
            $('#sidebar').on('click', function(e) {
                e.stopPropagation();
            });

            // Handle window resize
            $(window).resize(function() {
                initSidebar();
            });

            // Add active class to current menu item
            const currentPath = window.location.pathname;
            $('#sidebar ul.components a').each(function() {
                const href = $(this).attr('href');
                // Check if the href exactly matches the current path
                if (href === currentPath) {
                    $(this).addClass('active');
                }
                // For document files pages, highlight the Documents menu item
                else if (currentPath.includes('staff/office/documents/files/') && href === '<?= base_url('staff/office/documents') ?>') {
                    $(this).addClass('active');
                }
            });

            // Configure toastr options
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "timeOut": "3000"
            };

            // Display flash messages using toastr
            <?php if (session()->getFlashdata('success')) : ?>
                toastr.success('<?= session()->getFlashdata('success') ?>');
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')) : ?>
                toastr.error('<?= session()->getFlashdata('error') ?>');
            <?php endif; ?>

            <?php if (session()->getFlashdata('warning')) : ?>
                toastr.warning('<?= session()->getFlashdata('warning') ?>');
            <?php endif; ?>

            <?php if (session()->getFlashdata('info')) : ?>
                toastr.info('<?= session()->getFlashdata('info') ?>');
            <?php endif; ?>

            // Display validation errors using toastr
            <?php if (session()->has('errors')) : ?>
                <?php foreach (session('errors') as $error) : ?>
                    toastr.error('<?= esc($error) ?>');
                <?php endforeach; ?>
            <?php endif; ?>

            // Add debug console log on page load
            console.log('Document ready, Mobile view:', isMobileView());

            // Force a resize event after a slight delay to ensure proper initialization
            // Only trigger if no forms are being submitted
            setTimeout(function() {
                if (!$('form').hasClass('submitting')) {
                    $(window).trigger('resize');
                    console.log('Forced resize event');
                }
            }, 500);

            // Prevent template JS from interfering with form submissions
            $('form').on('submit', function() {
                $(this).addClass('submitting');
                console.log('Form submission started');
            });


        });
    </script>

    <!-- Additional Scripts -->
    <?= $this->renderSection('scripts') ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</body>

</html>